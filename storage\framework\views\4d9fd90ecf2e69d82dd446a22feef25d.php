<!doctype html>
<html lang="en" data-theme="<?php echo e($config->get('ui.theme', 'light')); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="color-scheme" content="<?php echo e($config->get('ui.theme', 'light')); ?>">
    <title><?php echo e($config->get('ui.title', config('app.name') . ' - API Docs')); ?></title>

    <script src="https://unpkg.com/@stoplight/elements@8.3.4/web-components.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/@stoplight/elements@8.3.4/styles.min.css">

    <script>
        const originalFetch = window.fetch;

        // intercept TryIt requests and add the XSRF-TOKEN header,
        // which is necessary for Sanctum cookie-based authentication to work correctly
        window.fetch = (url, options) => {
            const CSRF_TOKEN_COOKIE_KEY = "XSRF-TOKEN";
            const CSRF_TOKEN_HEADER_KEY = "X-XSRF-TOKEN";
            const getCookieValue = (key) => {
                const cookie = document.cookie.split(';').find((cookie) => cookie.trim().startsWith(key));
                return cookie?.split("=")[1];
            };

            const updateFetchHeaders = (
                headers,
                headerKey,
                headerValue,
            ) => {
                if (headers instanceof Headers) {
                    headers.set(headerKey, headerValue);
                } else if (Array.isArray(headers)) {
                    headers.push([headerKey, headerValue]);
                } else if (headers) {
                    headers[headerKey] = headerValue;
                }
            };
            const csrfToken = getCookieValue(CSRF_TOKEN_COOKIE_KEY);
            if (csrfToken) {
                const { headers = new Headers() } = options || {};
                updateFetchHeaders(headers, CSRF_TOKEN_HEADER_KEY, decodeURIComponent(csrfToken));
                return originalFetch(url, {
                    ...options,
                    headers,
                });
            }

            return originalFetch(url, options);
        };
    </script>
</head>
<body style="height: 100vh; overflow-y: hidden">
<elements-api
    id="docs"
    tryItCredentialsPolicy="<?php echo e($config->get('ui.try_it_credentials_policy', 'include')); ?>"
    router="hash"
    <?php if($config->get('ui.hide_try_it')): ?> hideTryIt="true" <?php endif; ?>
    <?php if($config->get('ui.hide_schemas')): ?> hideSchemas="true" <?php endif; ?>
    logo="<?php echo e($config->get('ui.logo')); ?>"
    <?php if($config->get('ui.layout')): ?> layout="<?php echo e($config->get('ui.layout')); ?>" <?php endif; ?>
/>
<script>
    (async () => {
        const docs = document.getElementById('docs');
        docs.apiDescriptionDocument = <?php echo json_encode($spec, 15, 512) ?>;
    })();
</script>
</body>
</html>
<?php /**PATH C:\laragon\www\shop\vendor\dedoc\scramble\src\/../resources/views/docs.blade.php ENDPATH**/ ?>