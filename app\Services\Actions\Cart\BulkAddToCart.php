<?php

namespace App\Services\Actions\Cart;

use App\Models\Shopping\ShoppingCart;
use App\Models\Product\Product;
use App\Models\Product\ProductVariant;
use App\Traits\Cart\CalculatesCartPricing;
use Illuminate\Support\Facades\DB;

/**
 * BulkAddToCart Action
 *
 * Handles adding multiple products to a user's shopping cart at once.
 * This action is responsible for:
 * - Creating or updating multiple cart items in a single transaction
 * - Handling authenticated user carts
 * - If a product already exists in the cart, it sets the quantity to the given value
 * - If a product doesn't exist in the cart, it creates a new cart item with the given quantity
 */
class BulkAddToCart
{
    use CalculatesCartPricing;
    /**
     * Process adding multiple items to the cart.
     *
     * @param array $data Input data containing:
     *                    - items: Array of items with:
     *                      - variant_id: (string) The ID of the product variant to add
     *                      - quantity: (int) The quantity to set, defaults to 1
     *                      - guarantee_id: (int|null) The ID of the guarantee to add, optional
     * @return \App\Models\Shopping\ShoppingCart The updated shopping cart with the new items added
     */
    public function handle(array $data)
    {
        // Get the authenticated user's ID directly
        $userId = auth()->id();
        $items = $data['items'] ?? [];

        // Use a database transaction to ensure all items are added atomically
        return DB::transaction(function () use ($userId, $items) {
            // Get or create a shopping cart for this user
            $cart = ShoppingCart::firstOrCreate(
                ['user_id' => $userId]
            );
            foreach ($items as $item) {
                $variantId = $item['variant_id'];
                $quantity = $item['quantity'] ?? 1;
                $guaranteeId = $item['guarantee_id'] ?? null;

                // Find the product variant
                $variant = ProductVariant::find($variantId);

                // Find the parent product with shop relationship
                $product = Product::with('shop')->find($variant->product_id);

                // Check if this product variant is already in the cart
                $existing = $cart->items()
                    ->where('product_id', $product->id)
                    ->where('product_variant_id', $variant->id)
                    ->first();

                if ($existing) {
                    // If item already exists in cart, set the quantity to the given value
                    // instead of adding to it
                    $existing->quantity = $quantity;

                    // Update guarantee data if provided
                    if (!empty($guaranteeId)) {
                        $guarantee = \App\Models\Product\Guarantee::find($guaranteeId);
                        if ($guarantee) {
                            $existing->guarantee_id = $guarantee->id;
                            $existing->guarantee_company_name = $guarantee->company_name;
                            $existing->guarantee_months = $guarantee->months;
                            $existing->guarantee_price = $guarantee->price;
                        }
                    } else {
                        // Clear guarantee data if no guarantee is provided
                        $existing->guarantee_id = null;
                        $existing->guarantee_company_name = null;
                        $existing->guarantee_months = null;
                        $existing->guarantee_price = null;
                    }

                    $existing->save();
                } else {
                    // If item doesn't exist in cart, create a new cart item
                    // We store a snapshot of product data to preserve pricing and details
                    $cartItemData = [
                        'product_id' => $product->id,
                        'product_variant_id' => $variant->id,
                        'name' => $product->title,
                        'price' => $variant->price,
                        'sale_price' => $variant->sale_price,
                        'price_unit_id' => $variant->price_unit_id,
                        'quantity' => $quantity,
                        'image' => $variant->image ?? $product->main_image ?? null,
                        'shop_id' => $product->shop_id,
                        'shop_name' => $product->shop->title ?? null,
                    ];

                    // Add guarantee data if provided
                    if (!empty($guaranteeId)) {
                        $guarantee = \App\Models\Product\Guarantee::find($guaranteeId);
                        if ($guarantee) {
                            $cartItemData['guarantee_id'] = $guarantee->id;
                            $cartItemData['guarantee_company_name'] = $guarantee->company_name;
                            $cartItemData['guarantee_months'] = $guarantee->months;
                            $cartItemData['guarantee_price'] = $guarantee->price;
                        }
                    }

                    $cart->items()->create($cartItemData);
                }
            }

            // Load relationships and calculate pricing data using trait
            $this->loadCartRelationships($cart);

            return $this->formatCartData($cart);
        });
    }
}
