<?php

namespace App\Http\Middleware;

use Closure;

class BlockWebRoutes
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle($request, Closure $next)
    {

        if ($request->is('docs/api') && config('app.doc_api_key')) {
            if ($request->input('X-DOC-TOKEN') == config('app.doc_api_key'))
                return $next($request);
        }
        if ($request->is('storage/uploads')) {
            return $next($request);
        }
        return response('', 204);

    }
}
