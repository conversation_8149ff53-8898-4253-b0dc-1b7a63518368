<?php

namespace App\Services\Actions\User;

use App\Models\User\UserNotification;

/**
 * Action class for marking all user notifications as read.
 * 
 * This action updates all unread notifications for the authenticated user
 * by setting is_read to true and read_at to the current timestamp.
 */
class MarkAllNotificationsAsRead
{
    /**
     * Mark all unread notifications as read for the authenticated user.
     *
     * @return array Array containing the count of updated notifications
     */
    public function handle(): array
    {
        // Get the authenticated user's ID directly
        $userId = auth()->id();

        // Update all unread notifications for this user
        $updatedCount = UserNotification::where('user_id', $userId)
            ->where('is_read', false)
            ->update([
                'is_read' => true,
                'read_at' => now()
            ]);

        return [
            'updated_count' => $updatedCount
        ];
    }
}
