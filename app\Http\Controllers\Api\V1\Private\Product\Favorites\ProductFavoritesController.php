<?php

namespace App\Http\Controllers\Api\V1\Private\Product\Favorites;

use App\Http\Controllers\Api\BaseController;
use App\Http\Requests\Product\Favorite\AddProductToFavoritesRequest;
use App\Http\Requests\Product\Favorite\DeleteUserFavoriteProductsRequest;
use App\Http\Requests\Product\Favorite\GetUserFavoriteProductsRequest;
use App\Http\Resources\Product\Favorite\FavoriteProductCollection;
use App\Services\Actions\Product\Favorite\AddProductToFavorites;
use App\Services\Actions\Product\Favorite\DeleteUserFavoriteProducts;
use App\Services\Actions\Product\Favorite\GetUserFavoriteProducts;
use Illuminate\Http\JsonResponse;

class ProductFavoritesController extends BaseController
{
    /**
     * Adds a product to user favorites
     * @param AddProductToFavoritesRequest $request
     * @param AddProductToFavorites $action
     * @return JsonResponse
     */
    public function store(AddProductToFavoritesRequest $request, AddProductToFavorites $action): JsonResponse
    {
        $succses = $action->handle($request->validated());
        if ($succses) {
            return $this->sendResponse(
                [],
                __('messages.product.succesful_favorite_add')
            );
        }
        return $this->sendError([], __("messages.product.fail_favorite_add"), );

    }
    /**
     * Get all user favorite products
     * @param GetUserFavoriteProductsRequest $request
     * @param GetUserFavoriteProducts $action
     * @return JsonResponse
     */
    public function index(GetUserFavoriteProductsRequest $request, GetUserFavoriteProducts $action): JsonResponse
    {
        $products = $action->handle($request->validated());

        return $this->sendResponse(
            new FavoriteProductCollection($products),
            __('messages.product.found')
        );

    }

    /**
     * Get all user favorite products
     * @param DeleteUserFavoriteProductsRequest $request
     * @param DeleteUserFavoriteProducts $action
     * @return JsonResponse
     */
    public function destroy(DeleteUserFavoriteProductsRequest $request, DeleteUserFavoriteProducts $action): JsonResponse
    {
        $products = $action->handle($request->validated());

        return $this->sendResponse(
            [],
            __('messages.product.succesful_favorite_remove')
        );

    }
}
