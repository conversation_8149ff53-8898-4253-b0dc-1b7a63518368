<?php

namespace App\Http\Resources\Product;

use App\Http\Resources\Content\GalleryResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\Product\ProductVariationResource;
use App\Http\Resources\Product\GuaranteeResource;
use App\Http\Resources\Product\DeliveryMethodResource;
use Illuminate\Support\Collection;
use Illuminate\Http\Resources\MissingValue;


/**
 * Resource class for transforming Product models into API responses.
 *
 * Provides a comprehensive representation of a product with all its related data,
 * including variants, attributes, gallery images, details, and more.
 */
class ProductResource extends JsonResource
{
    /**
     * Get product attributes to be included directly at the top level of the response
     *
     * @param Collection|null $variants
     * @return array
     */
    private function getProductAttributes($variants): array
    {
        if ($variants instanceof MissingValue || !$variants || $variants->isEmpty()) {
            return [];
        }

        // Get all attributes from all variants
        $allAttributes = $variants->flatMap(fn($v) => $v->attributes ?? collect());

        // Group attributes by type (e.g., 'color', 'size')
        $groupedAttributes = $allAttributes->groupBy('attribute_type');

        $result = [];

        foreach ($groupedAttributes as $attributeType => $attributes) {
            // Get unique attribute values
            $uniqueAttributes = $attributes->unique(fn($item) => $item['attribute_value'] ?? '')->values();

            // Only add attributes if there are any and attribute_type is not empty
            if ($uniqueAttributes->isNotEmpty() && !empty($attributeType)) {
                // Format all attributes as simple arrays [x,xl,s] or [red,blue]
                $key = $attributeType . 's'; // Add 's' for pluralization
                $result[$key] = $uniqueAttributes->pluck('attribute_value')->filter()->toArray();
            }
        }

        return $result;
    }

    /**
     * Get product attributes formatted as a structured block
     *
     * @param Collection|null $variants
     * @return array
     */
    private function getAttributesBlock($variants): array
    {
        if ($variants instanceof MissingValue || !$variants || $variants->isEmpty()) {
            return [];
        }

        // Get all attributes from all variants
        $allAttributes = $variants->flatMap(fn($v) => $v->attributes ?? collect());

        // Group attributes by type (e.g., 'color', 'size')
        $groupedAttributes = $allAttributes->groupBy('attribute_type');

        $attributesBlock = [];

        foreach ($groupedAttributes as $attributeType => $attributes) {
            // Skip if attribute type is empty
            if (empty($attributeType)) {
                continue;
            }

            // Get unique attribute values with their titles and extra data
            $uniqueAttributes = $attributes->unique(fn($item) => $item['attribute_value'] ?? '')->values();

            // Only add attributes if there are any
            if ($uniqueAttributes->isNotEmpty()) {
                $attributeValues = $uniqueAttributes->map(function ($attribute) {
                    $data = [
                        'value' => $attribute['attribute_value'] ?? '',
                    ];

                    // Add extra data if available (like color hex codes)
                    if (!empty($attribute['extra_data'])) {
                        $data['extra_data'] = $attribute['extra_data'];
                    }

                    return $data;
                })->filter(fn($item) => !empty($item['value']))->toArray();

                if (!empty($attributeValues)) {
                    $attributesBlock[] = [
                        'type' => $attributeType,
                        'title' => $uniqueAttributes->first()['attribute_title'] ?? ucfirst($attributeType),
                        'values' => $attributeValues
                    ];
                }
            }
        }

        return $attributesBlock;
    }

    /**
     * Get the default variant for the product
     *
     * This method selects the default variant based on the following criteria:
     * 1. The variant with the highest quantity in stock
     * 2. If quantities are equal, the one with the lowest price
     *
     * @param Collection|null $variants
     * @return ProductVariationResource|null
     */
    private function getDefaultVariant($variants)
    {

        if ($variants instanceof MissingValue || !$variants || $variants->isEmpty()) {
            return null;
        }

        // Sort variants by quantity (descending) and then by price (ascending)
        // Handle null values in sorting
        $sortedvariants = $variants->sortBy([
            fn($item) => $item->current_quantity ?? 0,
            fn($item) => $item->price ?? PHP_INT_MAX
        ], SORT_REGULAR, true);

        // Get the first variation after sorting
        $defaultVariant = $sortedvariants->first();

        return $defaultVariant ? new ProductVariationResource($defaultVariant) : null;
    }

    /**
     * Get comments count safely
     *
     * @return int
     */
    private function getCommentsCount(): int
    {
        try {
            return $this->comments ? $this->comments()->count() : 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get questions count safely
     *
     * @return int
     */
    private function getQuestionsCount(): int
    {
        try {
            return $this->questions ? $this->questions()->count() : 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get product rating information safely
     *
     * @return array
     */
    private function getProductRatingInfo(): array
    {
        try {
            if (!method_exists($this, 'comments') || !$this->comments) {
                return [
                    'product_rates_count' => 0,
                    'product_rate' => 5,
                ];
            }

            $ratedComments = $this->comments()
                ->where('has_bought', true)
                ->get();

            $productRatesCount = $ratedComments ? $ratedComments->count() : 0;
            $productAverageRate = $productRatesCount > 0
                ? round($ratedComments->avg('rate') ?? 5, 1)
                : 5;

            return [
                'product_rates_count' => $productRatesCount,
                'product_rate' => $productAverageRate,
            ];
        } catch (\Exception $e) {
            return [
                'product_rates_count' => 0,
                'product_rate' => null,
            ];
        }
    }

    /**
     * Transform the resource into an array.
     *
     * Includes:
     * - Basic product information (title, slug, description)
     * - Meta information (title, description, keywords)
     * - Gallery images
     * - Product details
     * - variants and default variant
     * - Product attributes (colors, sizes, etc.)
     * - category
     * - Guarantees
     * - Delivery methods
     * - Shop information
     * - Comment and question counts
     * - Product rating information
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $this->load('gallery', 'variants', 'keywords', 'details', 'category', 'guarantees', 'deliveryMethods', 'shop');
        $variants = $this->whenLoaded('variants', fn() => $this->variants);
        $ratingInfo = $this->getProductRatingInfo();

        return [

            // Guaranteed fields
            'id' => $this->id,
            'title' => $this->title,
            'slug' => $this->slug,
            'description' => $this->description,

            // Safely handled counts
            'comments_count' => $this->getCommentsCount(),
            'questions_count' => $this->getQuestionsCount(),

            // Rating information
            'product_rates_count' => $ratingInfo['product_rates_count'],
            'product_rate' => $ratingInfo['product_rate'],

            // Meta information with null safety
            'meta' => [
                'title' => $this->meta_title ?? null,
                'description' => $this->meta_description ?? null,
                'keywords' => $this->whenLoaded(
                    'keywords',
                    fn() => $this->keywords ? $this->keywords->pluck('title')->filter()->implode(', ') : null
                ) ?: null,
            ],

            // Gallery with null safety
            'galleries' => $this->whenLoaded(
                'gallery',
                fn() => GalleryResource::collection($this->gallery)
            ),

            // Details with null safety
            'details' => $this->whenLoaded(
                'details',
                fn() => $this->details ? $this->details->map(fn($detail) => [
                    'key' => $detail->key ?? null,
                    'value' => $detail->value ?? null,
                ])->filter(fn($detail) => !is_null($detail['key'])) : []
            ) ?: [],

            // variants
            'variants' => $variants ? ProductVariationResource::collection($variants) : [],

            'default_variant' => $this->getDefaultVariant($variants),

            // Add attributes block with all attribute types and values
            'attributes' => $this->getAttributesBlock($variants),

            // category with null safety
            'category' => $this->whenLoaded(
                'category',
                [
                    'title' => $this->category->title,
                    'slug' => $this->category->slug
                ]
            ),

            // Guarantees with null safety
            'guarantees' => $this->whenLoaded(
                'guarantees',
                fn() => $this->guarantees ? GuaranteeResource::collection($this->guarantees) : []
            ) ?: [],

            // Delivery methods with null safety
            'delivery_methods' => $this->whenLoaded(
                'deliveryMethods',
                fn() => $this->deliveryMethods ? DeliveryMethodResource::collection($this->deliveryMethods) : []
            ) ?: [],

            // Shop information with null safety
            'shop' => $this->whenLoaded(
                'shop',
                fn() => $this->shop ? [
                    'id' => (string) ($this->shop->id ?? $this->shop->id ?? ''),
                    'title' => $this->shop->title ?? null,
                ] : null
            ),
        ];
    }
}