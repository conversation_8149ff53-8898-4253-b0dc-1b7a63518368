<?php

namespace App\Services\Actions\Product\Favorite;

use App\Enums\Product\ProductSortOption;
use App\Models\Product\Product;
use App\Traits\Products\ProductQueryTrait;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;


class DeleteUserFavoriteProducts
{

    public function handle(array $data)
    {
        $user = auth()->user();
        $product = Product::where('slug', $data['slug'])->first();
        $user->favoriteProducts()->detach($product->id);
    }


}
