<?php

namespace App\Http\Requests\Product\Favorite;

use Illuminate\Foundation\Http\FormRequest;

class DeleteUserFavoriteProductsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'slug' => ['required', 'string', 'exists:products,slug'],
        ];
    }

    public function withcValidator($validator)
    {
        $validator->after(function ($validator) {
            $user = auth()->user();
            $favorite = $user->favoriteProducts()->where('slug', $this->validated('slug'))
                ->first();

            if (!$favorite) {
                $validator->errors()->add('id', __('messages.product.favorite_not_found'));
                return;
            }
        });
    }
}
